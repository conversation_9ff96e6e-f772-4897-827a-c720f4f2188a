<?php

namespace App\Repositories\SearchProduct;

use App\Helper\CacheHelper;
use App\Models\Filters;
use App\Models\Product;
use App\Models\ProductVisit;
use App\Models\User;
use App\Repositories\Attribute\AttributeRepositoryInterface;
use App\Repositories\BaseRepository;
use App\Repositories\SearchProduct\SearchRepositoryInterface;
use Aws\Exception\AwsException;
use Aws\PersonalizeRuntime\PersonalizeRuntimeClient;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Meilisearch\Endpoints\Indexes;
use ReflectionClass;

class SearchRepository extends BaseRepository implements SearchRepositoryInterface
{



    public $modelClass = Product::class;
    public $minutes = 10000;

    public function search(array $filters, int $page, int $limit, string $orderBy, string $query, int $filtersGroupsId): mixed
    {
        // Define relationships to be eager-loaded
        $with = [
            'covers',
            'variance' => function ($q) {
                $q->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $cacheHelper = new CacheHelper;


        $filters['isListed'] = true;

        $keys = $cacheHelper->remember(
            key: "attribute-keys",
            callback: function () {
                return resolve(AttributeRepositoryInterface::class)->getKeysFilterableAttributes();
            },
            tags: [],
            ttl: $this->minutes,
        );


        $facets = array_merge(Product::$filterableAttributes, $keys);



        list($orderBy, $orderDirection) = explode(',', $orderBy);

        // Build the search query
        $searchQuery = $this->modelClass::search($query, function (Indexes $meilisearch, $query, $options) use ($filters, $page, $limit, $facets) {
            // Set pagination and facet options
            $options['offset'] = ($page - 1) * $limit; // Calculate offset dynamically
            $options['limit'] = $limit;
            $options['facets'] = $facets;

            $this->applyFilters($options, $filters);
            return $meilisearch->search($query, $options);
        });

        if ('basePrice' == $orderBy) {
            $orderBy = 'minPrice';
        }


        $searchQuery->orderBy("hasStock", "DESC")->orderBy($orderBy, $orderDirection);

        $rawData = $searchQuery->raw();


        $filters = [];
        $products = $searchQuery->query(function ($query) use ($with) {
            $query->with($with);
        })->orderBy('score', 'DESC')->paginate(perPage: $limit, page: $page);




        $modelFilters = [];

        $keysFacetDistribution = array_keys($rawData['facetDistribution']);


        $filters = $cacheHelper->remember(
            key: "filters-attribute-$filtersGroupsId",
            callback: function () use ($keysFacetDistribution, $filtersGroupsId) {
                return \App\Models\Attribute::join('filters', 'filters.attributeId', '=', 'attributes.attributeId')
                    ->whereIn('attributes.key', $keysFacetDistribution)
                    ->where('filters.filtersGroupsId', $filtersGroupsId)
                    ->where('filters.filterType', 'attribute')
                    ->with(['options'])
                    ->get();
            },
            tags: [],
            ttl: $this->minutes,
        );

        foreach ($filters as $key => $filter) {
            $key = $filter->key;
            $class = 'App\Models\Filters\MeilisearchFilters\\AttributeFilter'::class;
            $modelFilters[$key] = new $class($filter);
            $modelFilters[$key]->setKey($key);
            if ($filter->filterType === 'attribute') {
                $modelFilters[$key]->setFacetsOptions($rawData['facetDistribution'][$filter->key]);
            }
        }

        $filter2 = $cacheHelper->remember(
            key: "filters-un-attribute-$filtersGroupsId",
            callback: function () use ($keysFacetDistribution, $filtersGroupsId) {
                return Filters::where('filtersGroupsId', $filtersGroupsId)->where('filterType', '!=', 'attribute')->get();
            },
            tags: [],
            ttl: $this->minutes,
        );

        foreach ($filter2 as $key => $f2) {
            $key = $f2->filterType;
            $class = 'App\Models\Filters\MeilisearchFilters\\' . str::ucfirst($f2->filterType) . 'Filter'::class;
            $modelFilters[$key] = new $class($f2);
            $modelFilters[$key]->setKey($key);


            if (in_array($f2->filterType, ['brandId', 'categories'])) {
                $modelFilters[$key]->setFacetsOptions($rawData['facetDistribution'][$f2->filterType]);
            }
        }


        $products->meilisearchFilters = $modelFilters;
        $reflection = new ReflectionClass($products);
        $totalProperty = $reflection->getProperty('total');
        $totalProperty->setAccessible(true);
        $totalProperty->setValue($products, $rawData['nbHits']); // Override the total
        $perPageProperty = $reflection->getProperty('perPage');
        $perPageProperty->setAccessible(true);
        $perPageProperty->setValue($products, $limit); // Override the perPage

        return $products;
    }

    public function products(array $filters, int $page, int $limit, string $orderBy, string $query): mixed
    {
        // Define relationships to be eager-loaded
        $with = [
            'covers',
            'variance' => function ($q) {
                $q->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $cacheHelper = new CacheHelper;
        $filters['isListed'] = true;
        $keys = $cacheHelper->remember(
            key: "attribute-keys",
            callback: function () {
                return resolve(AttributeRepositoryInterface::class)->getKeysFilterableAttributes();
            },
            tags: [],
            ttl: $this->minutes,
        );


        $facets = array_merge(Product::$filterableAttributes, $keys);



        list($orderBy, $orderDirection) = explode(',', $orderBy);

        // Build the search query
        $searchQuery = $this->modelClass::search($query, function (Indexes $meilisearch, $query, $options) use ($filters, $page, $limit, $facets) {
            // Set pagination and facet options
            $options['offset'] = ($page - 1) * $limit; // Calculate offset dynamically
            $options['limit'] = $limit;
            $options['facets'] = $facets;

            $this->applyFilters($options, $filters);
            return $meilisearch->search($query, $options);
        })
        ;

        if ('basePrice' == $orderBy) {
            $orderBy = 'minPrice';
        }

        $searchQuery->orderBy($orderBy, $orderDirection)->orderBy("hasStock", "DESC");


        $filters = [];
        $products = $searchQuery->query(function ($query) use ($with) {
            $query->with($with);
        })->paginate(perPage: $limit, page: $page);

        $rawData = $searchQuery->raw();

        $reflection = new ReflectionClass(objectOrClass: $products);
        $totalProperty = $reflection->getProperty('total');
        $totalProperty->setAccessible(true);
        $totalProperty->setValue($products, $rawData['nbHits']); // Override the total
        $perPageProperty = $reflection->getProperty('perPage');
        $perPageProperty->setAccessible(true);
        $perPageProperty->setValue($products, $limit); // Override the perPage

        return $products;
    }



    public function autocomplete(array $filters, int $limit, string $orderBy, string $query): mixed
    {

        $with = [
            'covers',
            'variance' => function ($q) {
                $q->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $filters['isListed'] = true;
        list($orderBy, $orderDirection) = explode(',', $orderBy);

        // Build the search query
        $searchQuery = $this->modelClass::search($query, function (Indexes $meilisearch, $query, $options) use ($filters, $limit) {

            $options['limit'] = $limit;
            $this->applyFilters($options, $filters);
            return $meilisearch->search($query, $options);
        });


        if ('basePrice' == $orderBy) {
            $orderBy = 'minPrice';
        }


        $searchQuery->orderBy($orderBy, $orderDirection)->orderBy("hasStock", "DESC");


        $filters = [];
        $products = $searchQuery->query(function ($query) use ($with) {
            $query->with($with);
        })->take($limit)->orderBy('score', 'DESC')->get();


        return $products;


    }


    /*************  ✨ Windsurf Command ⭐  *************/
    /**
     * Applies filters to the options array for a search query.
     *
     * This method modifies the provided options array to include filters based on
     * the given filters array. It transforms the filters into a format suitable
     * for a search query, supporting both single and multiple values.
     *
     * @param array $options Reference to the options array that will be modified
     *                       to include filters. The 'filter' key is initialized if
     *                       not already present.
     * @param array $filters An associative array of filters where keys are the
     *                       filter names and values are the filter criteria.
     *                       Values can be single values or arrays of values.
     */

    /*******  d1d7c6a0-8edb-4bfb-8cd2-0584eda93604  *******/
    protected function applyFilters(array &$options, array $filters): void
    {
        if (!isset($options['filter'])) {
            $options['filter'] = [];
        }

        $min = 0;
        $max = 0;

        foreach ($filters as $key => $filter) {
            if (is_array($filter)) {
                $filterValues = array_map(function ($value) {
                    return is_string($value) ? "'$value'" : $value;
                }, $filter);
                $options['filter'][] = "$key IN [" . implode(', ', $filterValues) . "]";
            } elseif ($key == 'price') {
                list($min, $max) = explode(':', $filters['price']);
                $options['filter'][] = "minPrice >= $min AND maxPrice <= $max";
            } elseif ($key == 'createdAt' && $filter instanceof \Carbon\Carbon) {
                // Handle createdAt filter for date comparisons
                $timestamp = $filter->timestamp;
                $options['filter'][] = "$key > $timestamp";
            } elseif (strpos($filter, ',') !== false) {
                $options['filter'][] = "$key IN [$filter]";
            } else {
                $filterValue = is_string($filter) && !is_numeric($filter) ? "'$filter'" : (is_bool($filter) ? ($filter ? 'true' : 'false') : $filter);
                $options['filter'][] = "$key = $filterValue";
            }

        }


    }



    public function mostPopular($limit): mixed
    {
        $with = [
            'covers',
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];
        $cacheHelper = new CacheHelper;
        return $cacheHelper->remember(
            key: "products-mostPopular_$limit",
            callback: function () use ($with, $limit) {
                $filters = [
                    'hasStock' => true,
                    'isListed' => true,
                ];
                $searchQuery = $this->modelClass::search('', function (Indexes $meilisearch, $query, $options) use ($filters, $limit) {
                    // Set pagination and facet options
                    $options['limit'] = $limit;
                    $this->applyFilters($options, $filters);
                    return $meilisearch->search($query, $options);
                })->orderBy('score', 'DESC');


                return $searchQuery->query(function ($query) use ($with) {
                    $query->with($with);
                })->get();


            },
            tags: [],
            ttl: $this->minutes,
        );

    }


    public function newArrival($limit): mixed
    {
        $with = [
            'covers',
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $cacheHelper = new CacheHelper;
        return $cacheHelper->remember(
            key: "products-newArrival-ml_$limit",
            callback: function () use ($with, $limit) {
                // Get products using PHP ML
                $filters = [
                    'hasStock' => true,
                    'isListed' => true,
                    'createdAt' => now()->subDays(30)
                ];

                // Get recent products as base dataset
                $recentProducts = $this->modelClass::where('isListed', true)
                    ->where('hasStock', true)
                    ->where('createdAt', '>', now()->subDays(30))
                    ->orderBy('createdAt', 'DESC')
                    ->limit($limit * 3) // Get more products for ML processing
                    ->get(['productId', 'priority', 'viewCount', 'minPrice']);

                // Apply simple ML scoring algorithm
                $scoredProducts = $recentProducts->map(function ($product) {
                    // Calculate a score based on recency, popularity and other factors
                    $daysOld = now()->diffInDays($product->createdAt);
                    $recencyScore = max(0, 100 - ($daysOld * 2)); // Newer products score higher
                    $popularityScore = min(100, $product->viewCount / 10);
                    $priceScore = min(100, max(0, 100 - ($product->minPrice / 100)));
                    $priorityScore = min(100, $product->priority * 10);

                    // Combined weighted score
                    $totalScore = ($recencyScore * 0.5) + ($popularityScore * 0.2) +
                        ($priceScore * 0.1) + ($priorityScore * 0.2);

                    return [
                        'productId' => $product->productId,
                        'score' => $totalScore
                    ];
                })
                    ->sortByDesc('score')
                    ->take($limit)
                    ->pluck('productId')
                    ->toArray();

                // Use the ML-selected product IDs in the filter
                if (count($scoredProducts) > 0) {
                    $filters['productId'] = $scoredProducts;
                }

                $searchQuery = $this->modelClass::search('', function (Indexes $meilisearch, $query, $options) use ($filters, $limit) {
                    $options['limit'] = $limit;
                    $this->applyFilters($options, $filters);
                    return $meilisearch->search($query, $options);
                })->orderBy('score', 'DESC');

                return $searchQuery->query(function ($query) use ($with) {
                    $query->with($with);
                })->get();
            },
            tags: [],
            ttl: $this->minutes,
        );
    }



    public function offers($limit): mixed
    {

        $with = [
            'covers',
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $filters = [
            'hasStock' => true,
            'isListed' => true,
            'hasOffer' => true,
        ];
        $searchQuery = $this->modelClass::search('', function (Indexes $meilisearch, $query, $options) use ($filters, $limit) {
            // Set pagination and facet options
            $options['limit'] = $limit;
            $this->applyFilters($options, $filters);
            return $meilisearch->search($query, $options);
        })->orderBy('score', 'DESC');


        return $searchQuery->query(function ($query) use ($with) {
            $query->with($with);
        })->get();


    }



    public function productsVisit($limit): mixed
    {
        $with = [
            'covers',
            'variance' => function ($query) {
                $query->where('isDefault', true);
            },
            'variance.activeStock',
            'variance.media',
            'activeStock'
        ];

        $modelName = null;
        if ($user = getUserCached()) {
            $modelName = $user->getMorphClass();
            $userId = $modelName === User::class ? "user_$user->userId" : "visitor_$user->visitorId";
        } else {
            $userId = "user_unknown";
        }

        $cacheHelper = new CacheHelper;

        return $cacheHelper->remember(
            key: "products-productsVisit_$userId" . "$limit",
            callback: function () use ($with, $limit, $user, $modelName) {
                if ($user) {
                    $productsId = $modelName === User::class ? ProductVisit::where('userId', $user->userId)->limit($limit)->pluck('productId') : ProductVisit::where('visitorId', $user->visitorId)->limit($limit)->pluck('productId');
                    return $this->modelClass::with($with)->where('isListed', true)->whereIn('productId', $productsId)->get();
                } else {
                    return $this->modelClass::with($with)->where('isListed', true)->where('hasStock', true)->orderBy('priority', 'DESC')->limit($limit)->get();
                }

            },
            tags: [],
            ttl: $this->minutes,
        );
    }









    public function suggestedProducts($productId, int $limit): mixed
    {

        $region = 'eu-west-1'; // Replace with your AWS region
        $personalizeArn = 'arn:aws:personalize:eu-west-1:574865621625:campaign/bought-together-campaign'; // Replace with your Campaign ARN.
        $filterArn = null;
        $promotionArn = null;
        $recommendedIds = [];
        $scores = [];
        $cacheHelper = new CacheHelper;

        return $cacheHelper->remember(
            key: "products-suggestedProducts_$productId",
            callback: function () use ($productId, $limit, $region, $personalizeArn, $filterArn, $promotionArn, &$recommendedIds, &$scores) {
                try {
                    $personalizeClient = new PersonalizeRuntimeClient([
                        'region' => $region,
                        'version' => 'latest',

                    ]);
                } catch (AwsException $e) {
                    throw new \Exception('Error creating Personalize client: ' . $e->getMessage());
                }

                $params = [
                    'campaignArn' => $personalizeArn,
                    'itemId' => "$productId",
                    'numResults' => $limit,
                    // ...existing code...
                ];

                try {
                    $result = $personalizeClient->getRecommendations($params);
                    if (!empty($result['itemList'])) {
                        foreach ($result['itemList'] as $item) {
                            $recommendedIds[] = $item['itemId'];
                            $scores[$item['itemId']] = $item['score'] ?? 0;
                        }
                    }
                } catch (AwsException $e) {
                    // ...existing code...
                }
                $with = [
                    'covers',
                    'variance' => function ($query) {
                        $query->where('isDefault', true);
                    },
                    'variance.activeStock',
                    'variance.media',
                    'activeStock'
                ];

                $filters = [
                    // 'hasStock' => true,
                    // 'isListed' => true,
                    'productId' => $recommendedIds,
                ];

                $searchQuery = $this->modelClass::search('', function (Indexes $meilisearch, $query, $options) use ($filters, $limit) {
                    // Set pagination and facet options
                    $options['limit'] = $limit;
                    $this->applyFilters($options, $filters);
                    return $meilisearch->search($query, $options);
                })->orderBy('score', 'DESC');

                return $searchQuery->query(function ($query) use ($with) {
                    $query->with($with);
                })->get();

            },
            tags: [],
            ttl: 60000,
        );



    }



}