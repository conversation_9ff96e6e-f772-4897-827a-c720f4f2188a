<?php

namespace App\Repositories\Label;

use App\Helper\SlugHelper;
use App\Models\Label;
use App\Repositories\BaseRepository;
use App\Repositories\Label\LabelRepositoryInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;


class LabelRepository extends BaseRepository implements LabelRepositoryInterface
{

    public $modelClass = Label::class;


    public function storeLabel(array $data): Model
    {

        $slug = Str::slug($data['name']['en'], '-');
        $slug = str_replace("--", "-", $slug);
        $slug = SlugHelper::slugChecker($slug, $this->modelClass::getTableName());
        $data['slug'] = $slug;
        $label = $this->store([
            "name" => $data['name'],
            "slug" => $slug,
            "color" => $data['color']
        ]);
        return $label;
    }

    public function updateLabel(int|string $id, array $data): Model
    {
        $label = $this->update($id, [
            "name" => $data['name'],
            "color" => $data['color']
        ]);

        return $label;
    }

    public function getAllLabels()
    {
        return $this->getAllLatestPaginated();
    }

    public function findByIdLabel(int|string $id): mixed
    {

        return $this->findById($id);

    }

    public function deleteLabel(int|string $id): mixed
    {
        return $this->destroy($id);
    }

    public function findBySlugLabel(string $slug): Label
    {
        return $this->query()->where('slug', '=', $slug)->first();
    }
}